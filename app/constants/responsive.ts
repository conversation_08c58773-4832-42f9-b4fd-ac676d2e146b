// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Responsive design constants and breakpoints for the mobile app
 */

// Screen size breakpoints (in pixels)
export const BREAKPOINTS = {
    // Small phones (iPhone SE, small Android phones)
    SMALL: 320,
    
    // Standard phones (iPhone 12, most Android phones)
    MEDIUM: 375,
    
    // Large phones (iPhone 12 Pro Max, large Android phones)
    LARGE: 414,
    
    // Small tablets (iPad mini, small Android tablets)
    TABLET_SMALL: 768,
    
    // Large tablets (iPad Pro, large Android tablets)
    TABLET_LARGE: 1024,
    
    // Desktop/very large screens
    DESKTOP: 1200,
} as const;

// Device type definitions
export const DEVICE_TYPES = {
    PHONE_SMALL: 'phone_small',
    PHONE_MEDIUM: 'phone_medium', 
    PHONE_LARGE: 'phone_large',
    TABLET_SMALL: 'tablet_small',
    TABLET_LARGE: 'tablet_large',
    DESKTOP: 'desktop',
} as const;

// Responsive spacing scale
export const RESPONSIVE_SPACING = {
    // Base spacing unit (can be scaled based on device)
    BASE_UNIT: 8,
    
    // Spacing multipliers for different device types
    MULTIPLIERS: {
        [DEVICE_TYPES.PHONE_SMALL]: 0.875,   // 7px base
        [DEVICE_TYPES.PHONE_MEDIUM]: 1,      // 8px base
        [DEVICE_TYPES.PHONE_LARGE]: 1.125,   // 9px base
        [DEVICE_TYPES.TABLET_SMALL]: 1.25,   // 10px base
        [DEVICE_TYPES.TABLET_LARGE]: 1.5,    // 12px base
        [DEVICE_TYPES.DESKTOP]: 1.75,        // 14px base
    },
} as const;

// Common responsive dimensions
export const RESPONSIVE_DIMENSIONS = {
    // Container max widths
    CONTAINER_MAX_WIDTH: {
        [DEVICE_TYPES.PHONE_SMALL]: '100%',
        [DEVICE_TYPES.PHONE_MEDIUM]: '100%',
        [DEVICE_TYPES.PHONE_LARGE]: '100%',
        [DEVICE_TYPES.TABLET_SMALL]: 600,
        [DEVICE_TYPES.TABLET_LARGE]: 800,
        [DEVICE_TYPES.DESKTOP]: 1000,
    },
    
    // Sidebar widths
    SIDEBAR_WIDTH: {
        [DEVICE_TYPES.PHONE_SMALL]: 280,
        [DEVICE_TYPES.PHONE_MEDIUM]: 300,
        [DEVICE_TYPES.PHONE_LARGE]: 320,
        [DEVICE_TYPES.TABLET_SMALL]: 350,
        [DEVICE_TYPES.TABLET_LARGE]: 400,
        [DEVICE_TYPES.DESKTOP]: 450,
    },
    
    // Navigation heights
    NAVIGATION_HEIGHT: {
        [DEVICE_TYPES.PHONE_SMALL]: 44,
        [DEVICE_TYPES.PHONE_MEDIUM]: 48,
        [DEVICE_TYPES.PHONE_LARGE]: 52,
        [DEVICE_TYPES.TABLET_SMALL]: 56,
        [DEVICE_TYPES.TABLET_LARGE]: 60,
        [DEVICE_TYPES.DESKTOP]: 64,
    },
    
    // Touch target minimum sizes
    TOUCH_TARGET_MIN: {
        [DEVICE_TYPES.PHONE_SMALL]: 44,
        [DEVICE_TYPES.PHONE_MEDIUM]: 44,
        [DEVICE_TYPES.PHONE_LARGE]: 48,
        [DEVICE_TYPES.TABLET_SMALL]: 48,
        [DEVICE_TYPES.TABLET_LARGE]: 52,
        [DEVICE_TYPES.DESKTOP]: 56,
    },
} as const;

// Typography scaling
export const RESPONSIVE_TYPOGRAPHY = {
    SCALE_FACTORS: {
        [DEVICE_TYPES.PHONE_SMALL]: 0.9,
        [DEVICE_TYPES.PHONE_MEDIUM]: 1,
        [DEVICE_TYPES.PHONE_LARGE]: 1.1,
        [DEVICE_TYPES.TABLET_SMALL]: 1.2,
        [DEVICE_TYPES.TABLET_LARGE]: 1.3,
        [DEVICE_TYPES.DESKTOP]: 1.4,
    },
} as const;

// Export types
export type DeviceType = typeof DEVICE_TYPES[keyof typeof DEVICE_TYPES];
export type Breakpoint = typeof BREAKPOINTS[keyof typeof BREAKPOINTS];

export default {
    BREAKPOINTS,
    DEVICE_TYPES,
    RESPONSIVE_SPACING,
    RESPONSIVE_DIMENSIONS,
    RESPONSIVE_TYPOGRAPHY,
};
