// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {useEffect, useState, useMemo} from 'react';
import {Dimensions, useWindowDimensions} from 'react-native';
import {
    getDeviceType,
    isTabletDevice,
    isPhoneDevice,
    getResponsiveValue,
    scaleValue,
    getResponsiveSpacing,
    getResponsiveFontSize,
    getResponsiveContainerWidth,
    getResponsiveColumns,
    matchesBreakpoint,
} from '@utils/responsive';
import {DEVICE_TYPES, RESPONSIVE_DIMENSIONS, type DeviceType} from '@constants/responsive';

/**
 * Hook to get current device type and responsive information
 */
export function useResponsiveInfo() {
    const dimensions = useWindowDimensions();
    
    return useMemo(() => ({
        width: dimensions.width,
        height: dimensions.height,
        deviceType: getDeviceType(dimensions.width),
        isTablet: isTabletDevice(dimensions.width),
        isPhone: isPhoneDevice(dimensions.width),
        isLandscape: dimensions.width > dimensions.height,
        isPortrait: dimensions.height >= dimensions.width,
    }), [dimensions.width, dimensions.height]);
}

/**
 * Hook to get responsive values based on current device type
 */
export function useResponsiveValue<T>(values: Partial<Record<DeviceType, T>>, fallback: T): T {
    const {width} = useWindowDimensions();
    
    return useMemo(() => {
        return getResponsiveValue(values, fallback, width);
    }, [values, fallback, width]);
}

/**
 * Hook to get responsive spacing
 */
export function useResponsiveSpacing(multiplier: number = 1): number {
    const {width} = useWindowDimensions();
    
    return useMemo(() => {
        return getResponsiveSpacing(multiplier, width);
    }, [multiplier, width]);
}

/**
 * Hook to get responsive font size
 */
export function useResponsiveFontSize(baseFontSize: number): number {
    const {width} = useWindowDimensions();
    
    return useMemo(() => {
        return getResponsiveFontSize(baseFontSize, width);
    }, [baseFontSize, width]);
}

/**
 * Hook to get responsive container width
 */
export function useResponsiveContainerWidth(): number | string {
    const {width} = useWindowDimensions();
    
    return useMemo(() => {
        return getResponsiveContainerWidth(width);
    }, [width]);
}

/**
 * Hook to get responsive dimensions for common UI elements
 */
export function useResponsiveDimensions() {
    const {width, deviceType, isTablet} = useResponsiveInfo();
    
    return useMemo(() => ({
        // Sidebar width
        sidebarWidth: getResponsiveValue(
            RESPONSIVE_DIMENSIONS.SIDEBAR_WIDTH,
            300,
            width
        ),
        
        // Navigation height
        navigationHeight: getResponsiveValue(
            RESPONSIVE_DIMENSIONS.NAVIGATION_HEIGHT,
            48,
            width
        ),
        
        // Touch target minimum size
        touchTargetMin: getResponsiveValue(
            RESPONSIVE_DIMENSIONS.TOUCH_TARGET_MIN,
            44,
            width
        ),
        
        // Container max width
        containerMaxWidth: getResponsiveContainerWidth(width),
        
        // Grid columns
        gridColumns: getResponsiveColumns(width),
        
        // Common spacing values
        spacing: {
            xs: getResponsiveSpacing(0.5, width),    // 4px base
            sm: getResponsiveSpacing(1, width),      // 8px base
            md: getResponsiveSpacing(2, width),      // 16px base
            lg: getResponsiveSpacing(3, width),      // 24px base
            xl: getResponsiveSpacing(4, width),      // 32px base
            xxl: getResponsiveSpacing(6, width),     // 48px base
        },
        
        // Common padding values
        padding: {
            screen: isTablet ? getResponsiveSpacing(3, width) : getResponsiveSpacing(2.5, width),
            container: isTablet ? getResponsiveSpacing(2.5, width) : getResponsiveSpacing(2, width),
            component: getResponsiveSpacing(2, width),
            small: getResponsiveSpacing(1, width),
        },
        
        // Common margin values
        margin: {
            section: isTablet ? getResponsiveSpacing(4, width) : getResponsiveSpacing(3, width),
            component: getResponsiveSpacing(2, width),
            small: getResponsiveSpacing(1, width),
        },
        
        // Border radius values
        borderRadius: {
            small: getResponsiveSpacing(0.5, width),
            medium: getResponsiveSpacing(1, width),
            large: getResponsiveSpacing(1.5, width),
        },
    }), [width, deviceType, isTablet]);
}

/**
 * Hook to check if screen matches specific breakpoints
 */
export function useBreakpoint() {
    const {width} = useWindowDimensions();
    
    return useMemo(() => ({
        isSmall: matchesBreakpoint('SMALL', width),
        isMedium: matchesBreakpoint('MEDIUM', width),
        isLarge: matchesBreakpoint('LARGE', width),
        isTabletSmall: matchesBreakpoint('TABLET_SMALL', width),
        isTabletLarge: matchesBreakpoint('TABLET_LARGE', width),
        isDesktop: matchesBreakpoint('DESKTOP', width),
    }), [width]);
}

/**
 * Hook for responsive styles with automatic updates
 */
export function useResponsiveStyles<T>(
    getStyles: (dimensions: ReturnType<typeof useResponsiveDimensions>, info: ReturnType<typeof useResponsiveInfo>) => T
): T {
    const dimensions = useResponsiveDimensions();
    const info = useResponsiveInfo();
    
    return useMemo(() => {
        return getStyles(dimensions, info);
    }, [getStyles, dimensions, info]);
}

/**
 * Hook to get responsive image dimensions
 */
export function useResponsiveImageDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidthPercentage: number = 0.9
) {
    const {width: screenWidth} = useWindowDimensions();
    const {isTablet} = useResponsiveInfo();
    
    return useMemo(() => {
        const maxWidth = screenWidth * maxWidthPercentage;
        const aspectRatio = originalHeight / originalWidth;
        
        let imageWidth = originalWidth;
        let imageHeight = originalHeight;
        
        // Scale down if image is too wide
        if (imageWidth > maxWidth) {
            imageWidth = maxWidth;
            imageHeight = imageWidth * aspectRatio;
        }
        
        // Apply device-specific scaling
        const scaleFactor = isTablet ? 1.2 : 1;
        imageWidth *= scaleFactor;
        imageHeight *= scaleFactor;
        
        return {
            width: Math.round(imageWidth),
            height: Math.round(imageHeight),
        };
    }, [originalWidth, originalHeight, maxWidthPercentage, screenWidth, isTablet]);
}

/**
 * Hook for responsive toast dimensions
 */
export function useResponsiveToastDimensions() {
    const {isTablet} = useResponsiveInfo();
    const dimensions = useResponsiveDimensions();
    
    return useMemo(() => ({
        width: isTablet ? 484 : Math.min(400, dimensions.containerMaxWidth as number - 40),
        height: getResponsiveValue(
            {
                [DEVICE_TYPES.PHONE_SMALL]: 52,
                [DEVICE_TYPES.PHONE_MEDIUM]: 56,
                [DEVICE_TYPES.PHONE_LARGE]: 60,
                [DEVICE_TYPES.TABLET_SMALL]: 64,
                [DEVICE_TYPES.TABLET_LARGE]: 68,
                [DEVICE_TYPES.DESKTOP]: 72,
            },
            56
        ),
        margin: dimensions.spacing.md,
    }), [isTablet, dimensions]);
}
