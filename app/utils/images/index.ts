// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {Dimensions} from 'react-native';

import {View} from '@constants';
import {IMAGE_MAX_HEIGHT, IMAGE_MIN_DIMENSION, MAX_GIF_SIZE, VIEWPORT_IMAGE_OFFSET, VIEWPORT_IMAGE_REPLY_OFFSET} from '@constants/image';
import {getDeviceType, getResponsiveValue} from '@utils/responsive';
import {DEVICE_TYPES} from '@constants/responsive';

export const calculateDimensions = (height?: number, width?: number, viewPortWidth = 0, viewPortHeight = 0) => {
    'worklet';

    if (!height || !width) {
        return {
            height: 0,
            width: 0,
        };
    }

    const ratio = height / width;
    const heightRatio = width / height;

    let imageWidth = width;
    let imageHeight = height;

    if (width >= viewPortWidth) {
        imageWidth = viewPortWidth;
        imageHeight = imageWidth * ratio;
    } else if (width < IMAGE_MIN_DIMENSION) {
        imageWidth = IMAGE_MIN_DIMENSION;
        imageHeight = imageWidth * ratio;
    }

    if ((imageHeight > IMAGE_MAX_HEIGHT || (viewPortHeight && imageHeight > viewPortHeight)) && viewPortHeight <= IMAGE_MAX_HEIGHT) {
        imageHeight = viewPortHeight || IMAGE_MAX_HEIGHT;
        imageWidth = imageHeight * heightRatio;
    } else if (imageHeight < IMAGE_MIN_DIMENSION && IMAGE_MIN_DIMENSION * heightRatio <= viewPortWidth) {
        imageHeight = IMAGE_MIN_DIMENSION;
        imageWidth = imageHeight * heightRatio;
    } else if (viewPortHeight && imageHeight > viewPortHeight) {
        imageHeight = viewPortHeight;
        imageWidth = imageHeight * heightRatio;
    }

    return {
        height: imageHeight,
        width: imageWidth,
    };
};

/**
 * Calculate responsive image dimensions based on device type
 */
export const calculateResponsiveDimensions = (
    height?: number,
    width?: number,
    viewPortWidth = 0,
    viewPortHeight = 0,
    deviceWidth?: number
) => {
    'worklet';

    if (!height || !width) {
        return {
            height: 0,
            width: 0,
        };
    }

    const deviceType = getDeviceType(deviceWidth);

    // Get responsive image constraints based on device type
    const maxHeight = getResponsiveValue({
        [DEVICE_TYPES.PHONE_SMALL]: IMAGE_MAX_HEIGHT * 0.8,
        [DEVICE_TYPES.PHONE_MEDIUM]: IMAGE_MAX_HEIGHT,
        [DEVICE_TYPES.PHONE_LARGE]: IMAGE_MAX_HEIGHT * 1.1,
        [DEVICE_TYPES.TABLET_SMALL]: IMAGE_MAX_HEIGHT * 1.3,
        [DEVICE_TYPES.TABLET_LARGE]: IMAGE_MAX_HEIGHT * 1.5,
        [DEVICE_TYPES.DESKTOP]: IMAGE_MAX_HEIGHT * 1.8,
    }, IMAGE_MAX_HEIGHT, deviceWidth);

    const minDimension = getResponsiveValue({
        [DEVICE_TYPES.PHONE_SMALL]: IMAGE_MIN_DIMENSION * 0.9,
        [DEVICE_TYPES.PHONE_MEDIUM]: IMAGE_MIN_DIMENSION,
        [DEVICE_TYPES.PHONE_LARGE]: IMAGE_MIN_DIMENSION * 1.1,
        [DEVICE_TYPES.TABLET_SMALL]: IMAGE_MIN_DIMENSION * 1.2,
        [DEVICE_TYPES.TABLET_LARGE]: IMAGE_MIN_DIMENSION * 1.4,
        [DEVICE_TYPES.DESKTOP]: IMAGE_MIN_DIMENSION * 1.6,
    }, IMAGE_MIN_DIMENSION, deviceWidth);

    const ratio = height / width;
    const heightRatio = width / height;

    let imageWidth = width;
    let imageHeight = height;

    // Apply responsive scaling
    if (width >= viewPortWidth) {
        imageWidth = viewPortWidth;
        imageHeight = imageWidth * ratio;
    } else if (width < minDimension) {
        imageWidth = minDimension;
        imageHeight = imageWidth * ratio;
    }

    if ((imageHeight > maxHeight || (viewPortHeight && imageHeight > viewPortHeight)) && viewPortHeight <= maxHeight) {
        imageHeight = viewPortHeight || maxHeight;
        imageWidth = imageHeight * heightRatio;
    } else if (imageHeight < minDimension && minDimension * heightRatio <= viewPortWidth) {
        imageHeight = minDimension;
        imageWidth = imageHeight * heightRatio;
    } else if (viewPortHeight && imageHeight > viewPortHeight) {
        imageHeight = viewPortHeight;
        imageWidth = imageHeight * heightRatio;
    }

    return {
        height: imageHeight,
        width: imageWidth,
    };
};

export function getViewPortWidth(isReplyPost: boolean, tabletOffset = false) {
    const {width, height} = Dimensions.get('window');
    let portraitPostWidth = Math.min(width, height) - VIEWPORT_IMAGE_OFFSET;

    if (tabletOffset) {
        portraitPostWidth -= View.TABLET_SIDEBAR_WIDTH;
    }

    if (isReplyPost) {
        portraitPostWidth -= VIEWPORT_IMAGE_REPLY_OFFSET;
    }

    return portraitPostWidth;
}

/**
 * Get responsive viewport width based on device type
 */
export function getResponsiveViewPortWidth(isReplyPost: boolean, tabletOffset = false, deviceWidth?: number) {
    const {width, height} = Dimensions.get('window');
    const screenWidth = deviceWidth || width;

    // Get responsive viewport offset
    const viewportOffset = getResponsiveValue({
        [DEVICE_TYPES.PHONE_SMALL]: VIEWPORT_IMAGE_OFFSET * 0.8,
        [DEVICE_TYPES.PHONE_MEDIUM]: VIEWPORT_IMAGE_OFFSET,
        [DEVICE_TYPES.PHONE_LARGE]: VIEWPORT_IMAGE_OFFSET * 1.1,
        [DEVICE_TYPES.TABLET_SMALL]: VIEWPORT_IMAGE_OFFSET * 1.2,
        [DEVICE_TYPES.TABLET_LARGE]: VIEWPORT_IMAGE_OFFSET * 1.4,
        [DEVICE_TYPES.DESKTOP]: VIEWPORT_IMAGE_OFFSET * 1.6,
    }, VIEWPORT_IMAGE_OFFSET, screenWidth);

    let portraitPostWidth = Math.min(width, height) - viewportOffset;

    if (tabletOffset) {
        // Use responsive sidebar width instead of hardcoded constant
        const responsiveSidebarWidth = getResponsiveValue({
            [DEVICE_TYPES.TABLET_SMALL]: 300,
            [DEVICE_TYPES.TABLET_LARGE]: 350,
            [DEVICE_TYPES.DESKTOP]: 400,
        }, View.TABLET_SIDEBAR_WIDTH, screenWidth);

        portraitPostWidth -= responsiveSidebarWidth;
    }

    if (isReplyPost) {
        const replyOffset = getResponsiveValue({
            [DEVICE_TYPES.PHONE_SMALL]: VIEWPORT_IMAGE_REPLY_OFFSET * 0.8,
            [DEVICE_TYPES.PHONE_MEDIUM]: VIEWPORT_IMAGE_REPLY_OFFSET,
            [DEVICE_TYPES.PHONE_LARGE]: VIEWPORT_IMAGE_REPLY_OFFSET * 1.1,
            [DEVICE_TYPES.TABLET_SMALL]: VIEWPORT_IMAGE_REPLY_OFFSET * 1.2,
            [DEVICE_TYPES.TABLET_LARGE]: VIEWPORT_IMAGE_REPLY_OFFSET * 1.4,
            [DEVICE_TYPES.DESKTOP]: VIEWPORT_IMAGE_REPLY_OFFSET * 1.6,
        }, VIEWPORT_IMAGE_REPLY_OFFSET, screenWidth);

        portraitPostWidth -= replyOffset;
    }

    return portraitPostWidth;
}

// isGifTooLarge returns true if we think that the GIF may cause the device to run out of memory when rendered
// based on the image's dimensions and frame count.
export function isGifTooLarge(imageMetadata?: PostImage) {
    if (imageMetadata?.format !== 'gif') {
        // Not a gif or from an older server that doesn't count frames
        return false;
    }

    const {frame_count: frameCount, height, width} = imageMetadata;

    // Try to estimate the in-memory size of the gif to prevent the device out of memory
    return width * height * (frameCount || 1) > MAX_GIF_SIZE;
}
