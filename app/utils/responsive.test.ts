// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {
    getDeviceType,
    isTabletDevice,
    isPhoneDevice,
    getResponsiveValue,
    scaleValue,
    getResponsiveSpacing,
    getResponsiveFontSize,
    getResponsiveContainerWidth,
    matchesBreakpoint,
    getResponsiveColumns,
} from './responsive';
import {BREAKPOINTS, DEVICE_TYPES} from '@constants/responsive';

describe('Responsive Utilities', () => {
    describe('getDeviceType', () => {
        it('should return correct device type for different widths', () => {
            expect(getDeviceType(300)).toBe(DEVICE_TYPES.PHONE_SMALL);
            expect(getDeviceType(350)).toBe(DEVICE_TYPES.PHONE_MEDIUM);
            expect(getDeviceType(400)).toBe(DEVICE_TYPES.PHONE_LARGE);
            expect(getDeviceType(800)).toBe(DEVICE_TYPES.TABLET_SMALL);
            expect(getDeviceType(1100)).toBe(DEVICE_TYPES.TABLET_LARGE);
            expect(getDeviceType(1300)).toBe(DEVICE_TYPES.DESKTOP);
        });
    });

    describe('isTabletDevice', () => {
        it('should correctly identify tablet devices', () => {
            expect(isTabletDevice(300)).toBe(false);
            expect(isTabletDevice(400)).toBe(false);
            expect(isTabletDevice(800)).toBe(true);
            expect(isTabletDevice(1100)).toBe(true);
            expect(isTabletDevice(1300)).toBe(true);
        });
    });

    describe('isPhoneDevice', () => {
        it('should correctly identify phone devices', () => {
            expect(isPhoneDevice(300)).toBe(true);
            expect(isPhoneDevice(400)).toBe(true);
            expect(isPhoneDevice(800)).toBe(false);
            expect(isPhoneDevice(1100)).toBe(false);
        });
    });

    describe('getResponsiveValue', () => {
        it('should return correct value for device type', () => {
            const values = {
                [DEVICE_TYPES.PHONE_SMALL]: 10,
                [DEVICE_TYPES.PHONE_MEDIUM]: 12,
                [DEVICE_TYPES.TABLET_SMALL]: 16,
                [DEVICE_TYPES.TABLET_LARGE]: 20,
            };

            expect(getResponsiveValue(values, 14, 300)).toBe(10);
            expect(getResponsiveValue(values, 14, 350)).toBe(12);
            expect(getResponsiveValue(values, 14, 800)).toBe(16);
            expect(getResponsiveValue(values, 14, 1100)).toBe(20);
        });

        it('should return fallback for missing device type', () => {
            const values = {
                [DEVICE_TYPES.PHONE_MEDIUM]: 12,
            };

            expect(getResponsiveValue(values, 14, 300)).toBe(12); // Falls back to phone medium
            expect(getResponsiveValue(values, 14, 1300)).toBe(14); // Falls back to default
        });
    });

    describe('scaleValue', () => {
        it('should scale values correctly', () => {
            const scaleFactors = {
                [DEVICE_TYPES.PHONE_SMALL]: 0.8,
                [DEVICE_TYPES.PHONE_MEDIUM]: 1,
                [DEVICE_TYPES.TABLET_SMALL]: 1.2,
            };

            expect(scaleValue(100, scaleFactors, 300)).toBe(80);
            expect(scaleValue(100, scaleFactors, 350)).toBe(100);
            expect(scaleValue(100, scaleFactors, 800)).toBe(120);
        });
    });

    describe('getResponsiveSpacing', () => {
        it('should return correct spacing for different devices', () => {
            const spacing1x = getResponsiveSpacing(1, 300);
            const spacing2x = getResponsiveSpacing(2, 300);
            const tabletSpacing = getResponsiveSpacing(1, 800);

            expect(spacing2x).toBe(spacing1x * 2);
            expect(tabletSpacing).toBeGreaterThan(spacing1x);
        });
    });

    describe('getResponsiveFontSize', () => {
        it('should scale font sizes correctly', () => {
            const phoneFontSize = getResponsiveFontSize(16, 350);
            const tabletFontSize = getResponsiveFontSize(16, 800);

            expect(tabletFontSize).toBeGreaterThan(phoneFontSize);
        });
    });

    describe('getResponsiveContainerWidth', () => {
        it('should return correct container width', () => {
            const phoneWidth = getResponsiveContainerWidth(350);
            const tabletWidth = getResponsiveContainerWidth(800);

            expect(phoneWidth).toBe('100%');
            expect(typeof tabletWidth).toBe('number');
        });
    });

    describe('matchesBreakpoint', () => {
        it('should correctly match breakpoints', () => {
            expect(matchesBreakpoint('SMALL', 300)).toBe(false);
            expect(matchesBreakpoint('SMALL', 350)).toBe(true);
            expect(matchesBreakpoint('TABLET_SMALL', 800)).toBe(true);
            expect(matchesBreakpoint('TABLET_SMALL', 700)).toBe(false);
        });
    });

    describe('getResponsiveColumns', () => {
        it('should return correct column count for devices', () => {
            expect(getResponsiveColumns(300)).toBe(1);
            expect(getResponsiveColumns(350)).toBe(1);
            expect(getResponsiveColumns(800)).toBe(2);
            expect(getResponsiveColumns(1100)).toBe(3);
            expect(getResponsiveColumns(1300)).toBe(4);
        });
    });
});

describe('Responsive Constants', () => {
    describe('BREAKPOINTS', () => {
        it('should have correct breakpoint values', () => {
            expect(BREAKPOINTS.SMALL).toBe(320);
            expect(BREAKPOINTS.MEDIUM).toBe(375);
            expect(BREAKPOINTS.LARGE).toBe(414);
            expect(BREAKPOINTS.TABLET_SMALL).toBe(768);
            expect(BREAKPOINTS.TABLET_LARGE).toBe(1024);
            expect(BREAKPOINTS.DESKTOP).toBe(1200);
        });

        it('should have breakpoints in ascending order', () => {
            const values = Object.values(BREAKPOINTS);
            const sortedValues = [...values].sort((a, b) => a - b);
            expect(values).toEqual(sortedValues);
        });
    });

    describe('DEVICE_TYPES', () => {
        it('should have all required device types', () => {
            expect(DEVICE_TYPES.PHONE_SMALL).toBeDefined();
            expect(DEVICE_TYPES.PHONE_MEDIUM).toBeDefined();
            expect(DEVICE_TYPES.PHONE_LARGE).toBeDefined();
            expect(DEVICE_TYPES.TABLET_SMALL).toBeDefined();
            expect(DEVICE_TYPES.TABLET_LARGE).toBeDefined();
            expect(DEVICE_TYPES.DESKTOP).toBeDefined();
        });
    });
});
