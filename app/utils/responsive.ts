// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {Dimensions, Platform} from 'react-native';
import {BREAKPOINTS, DEVICE_TYPES, type DeviceType} from '@constants/responsive';

/**
 * Get the current device type based on screen width
 */
export function getDeviceType(width?: number): DeviceType {
    const screenWidth = width || Dimensions.get('window').width;
    
    if (screenWidth <= BREAKPOINTS.SMALL) {
        return DEVICE_TYPES.PHONE_SMALL;
    } else if (screenWidth <= BREAKPOINTS.MEDIUM) {
        return DEVICE_TYPES.PHONE_MEDIUM;
    } else if (screenWidth <= BREAKPOINTS.LARGE) {
        return DEVICE_TYPES.PHONE_LARGE;
    } else if (screenWidth <= BREAKPOINTS.TABLET_SMALL) {
        return DEVICE_TYPES.TABLET_SMALL;
    } else if (screenWidth <= BREAKPOINTS.TABLET_LARGE) {
        return DEVICE_TYPES.TABLET_LARGE;
    } else {
        return DEVICE_TYPES.DESKTOP;
    }
}

/**
 * Check if current device is a tablet
 */
export function isTabletDevice(width?: number): boolean {
    const deviceType = getDeviceType(width);
    return deviceType === DEVICE_TYPES.TABLET_SMALL || 
           deviceType === DEVICE_TYPES.TABLET_LARGE || 
           deviceType === DEVICE_TYPES.DESKTOP;
}

/**
 * Check if current device is a phone
 */
export function isPhoneDevice(width?: number): boolean {
    return !isTabletDevice(width);
}

/**
 * Get responsive value based on device type
 */
export function getResponsiveValue<T>(values: Partial<Record<DeviceType, T>>, fallback: T, width?: number): T {
    const deviceType = getDeviceType(width);
    
    // Try to get exact match first
    if (values[deviceType] !== undefined) {
        return values[deviceType]!;
    }
    
    // Fallback logic for missing device types
    if (isTabletDevice(width)) {
        // For tablets, try tablet values first, then desktop, then fallback
        return values[DEVICE_TYPES.TABLET_LARGE] ?? 
               values[DEVICE_TYPES.TABLET_SMALL] ?? 
               values[DEVICE_TYPES.DESKTOP] ?? 
               fallback;
    } else {
        // For phones, try phone values first, then fallback
        return values[DEVICE_TYPES.PHONE_LARGE] ?? 
               values[DEVICE_TYPES.PHONE_MEDIUM] ?? 
               values[DEVICE_TYPES.PHONE_SMALL] ?? 
               fallback;
    }
}

/**
 * Scale a value based on device type
 */
export function scaleValue(baseValue: number, scaleFactor: Partial<Record<DeviceType, number>>, width?: number): number {
    const deviceType = getDeviceType(width);
    const factor = getResponsiveValue(scaleFactor, 1, width);
    return Math.round(baseValue * factor);
}

/**
 * Get responsive spacing value
 */
export function getResponsiveSpacing(multiplier: number = 1, width?: number): number {
    const deviceType = getDeviceType(width);
    const baseUnit = 8; // Base spacing unit
    
    const deviceMultipliers = {
        [DEVICE_TYPES.PHONE_SMALL]: 0.875,
        [DEVICE_TYPES.PHONE_MEDIUM]: 1,
        [DEVICE_TYPES.PHONE_LARGE]: 1.125,
        [DEVICE_TYPES.TABLET_SMALL]: 1.25,
        [DEVICE_TYPES.TABLET_LARGE]: 1.5,
        [DEVICE_TYPES.DESKTOP]: 1.75,
    };
    
    const deviceMultiplier = getResponsiveValue(deviceMultipliers, 1, width);
    return Math.round(baseUnit * deviceMultiplier * multiplier);
}

/**
 * Get responsive font size
 */
export function getResponsiveFontSize(baseFontSize: number, width?: number): number {
    const scaleFactors = {
        [DEVICE_TYPES.PHONE_SMALL]: 0.9,
        [DEVICE_TYPES.PHONE_MEDIUM]: 1,
        [DEVICE_TYPES.PHONE_LARGE]: 1.1,
        [DEVICE_TYPES.TABLET_SMALL]: 1.2,
        [DEVICE_TYPES.TABLET_LARGE]: 1.3,
        [DEVICE_TYPES.DESKTOP]: 1.4,
    };
    
    return scaleValue(baseFontSize, scaleFactors, width);
}

/**
 * Get responsive container width
 */
export function getResponsiveContainerWidth(width?: number): number | string {
    const screenWidth = width || Dimensions.get('window').width;
    const deviceType = getDeviceType(width);
    
    const maxWidths = {
        [DEVICE_TYPES.PHONE_SMALL]: '100%',
        [DEVICE_TYPES.PHONE_MEDIUM]: '100%',
        [DEVICE_TYPES.PHONE_LARGE]: '100%',
        [DEVICE_TYPES.TABLET_SMALL]: Math.min(screenWidth * 0.9, 600),
        [DEVICE_TYPES.TABLET_LARGE]: Math.min(screenWidth * 0.85, 800),
        [DEVICE_TYPES.DESKTOP]: Math.min(screenWidth * 0.8, 1000),
    };
    
    return getResponsiveValue(maxWidths, '100%', width);
}

/**
 * Platform-aware responsive value
 */
export function getPlatformResponsiveValue<T>(
    values: {
        ios?: Partial<Record<DeviceType, T>>;
        android?: Partial<Record<DeviceType, T>>;
        default?: Partial<Record<DeviceType, T>>;
    },
    fallback: T,
    width?: number
): T {
    const platformValues = Platform.select({
        ios: values.ios,
        android: values.android,
        default: values.default,
    }) || values.default || {};
    
    return getResponsiveValue(platformValues, fallback, width);
}

/**
 * Check if screen width matches a breakpoint
 */
export function matchesBreakpoint(breakpoint: keyof typeof BREAKPOINTS, width?: number): boolean {
    const screenWidth = width || Dimensions.get('window').width;
    return screenWidth >= BREAKPOINTS[breakpoint];
}

/**
 * Get responsive grid columns
 */
export function getResponsiveColumns(width?: number): number {
    const deviceType = getDeviceType(width);
    
    const columns = {
        [DEVICE_TYPES.PHONE_SMALL]: 1,
        [DEVICE_TYPES.PHONE_MEDIUM]: 1,
        [DEVICE_TYPES.PHONE_LARGE]: 1,
        [DEVICE_TYPES.TABLET_SMALL]: 2,
        [DEVICE_TYPES.TABLET_LARGE]: 3,
        [DEVICE_TYPES.DESKTOP]: 4,
    };
    
    return getResponsiveValue(columns, 1, width);
}
