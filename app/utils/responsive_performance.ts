// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {Dimensions} from 'react-native';
import {getDeviceType} from './responsive';

// Cache for device type to avoid repeated calculations
let cachedDeviceType: string | null = null;
let cachedScreenWidth: number | null = null;

/**
 * Get cached device type to improve performance
 */
export function getCachedDeviceType(width?: number): string {
    const screenWidth = width || Dimensions.get('window').width;
    
    // Return cached value if screen width hasn't changed
    if (cachedDeviceType && cachedScreenWidth === screenWidth) {
        return cachedDeviceType;
    }
    
    // Update cache
    cachedDeviceType = getDeviceType(screenWidth);
    cachedScreenWidth = screenWidth;
    
    return cachedDeviceType;
}

/**
 * Clear device type cache (useful for orientation changes)
 */
export function clearDeviceTypeCache(): void {
    cachedDeviceType = null;
    cachedScreenWidth = null;
}

/**
 * Debounce function for responsive calculations
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}

/**
 * Throttle function for responsive calculations
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Memoization utility for responsive calculations
 */
export function memoize<T extends (...args: any[]) => any>(
    func: T,
    getKey?: (...args: Parameters<T>) => string
): T {
    const cache = new Map<string, ReturnType<T>>();
    
    return ((...args: Parameters<T>) => {
        const key = getKey ? getKey(...args) : JSON.stringify(args);
        
        if (cache.has(key)) {
            return cache.get(key);
        }
        
        const result = func(...args);
        cache.set(key, result);
        
        return result;
    }) as T;
}

/**
 * Performance monitoring for responsive calculations
 */
export class ResponsivePerformanceMonitor {
    private static measurements: Map<string, number[]> = new Map();
    
    static startMeasurement(name: string): () => void {
        const startTime = performance.now();
        
        return () => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            if (!this.measurements.has(name)) {
                this.measurements.set(name, []);
            }
            
            this.measurements.get(name)!.push(duration);
        };
    }
    
    static getAverageTime(name: string): number {
        const times = this.measurements.get(name);
        if (!times || times.length === 0) {
            return 0;
        }
        
        return times.reduce((sum, time) => sum + time, 0) / times.length;
    }
    
    static getStats(name: string): {
        average: number;
        min: number;
        max: number;
        count: number;
    } {
        const times = this.measurements.get(name) || [];
        
        if (times.length === 0) {
            return { average: 0, min: 0, max: 0, count: 0 };
        }
        
        return {
            average: times.reduce((sum, time) => sum + time, 0) / times.length,
            min: Math.min(...times),
            max: Math.max(...times),
            count: times.length,
        };
    }
    
    static clearMeasurements(name?: string): void {
        if (name) {
            this.measurements.delete(name);
        } else {
            this.measurements.clear();
        }
    }
    
    static getAllStats(): Record<string, ReturnType<typeof ResponsivePerformanceMonitor.getStats>> {
        const stats: Record<string, ReturnType<typeof ResponsivePerformanceMonitor.getStats>> = {};
        
        for (const [name] of this.measurements) {
            stats[name] = this.getStats(name);
        }
        
        return stats;
    }
}

/**
 * HOC for performance monitoring of responsive components
 */
export function withResponsivePerformanceMonitoring<P extends object>(
    Component: React.ComponentType<P>,
    componentName: string
): React.ComponentType<P> {
    return (props: P) => {
        const endMeasurement = ResponsivePerformanceMonitor.startMeasurement(componentName);
        
        React.useEffect(() => {
            return endMeasurement;
        });
        
        return React.createElement(Component, props);
    };
}

/**
 * Hook for performance monitoring
 */
export function useResponsivePerformanceMonitoring(name: string): void {
    React.useEffect(() => {
        const endMeasurement = ResponsivePerformanceMonitor.startMeasurement(name);
        return endMeasurement;
    }, [name]);
}

// Initialize dimension change listener to clear cache
Dimensions.addEventListener('change', () => {
    clearDeviceTypeCache();
});
