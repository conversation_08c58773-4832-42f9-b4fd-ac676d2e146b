// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useEffect, useState} from 'react';
import {DeviceEventEmitter} from 'react-native';

import {TabletLayout} from '@components/responsive_layout';
import {Events, Screens} from '@constants';
import {useResponsiveInfo} from '@hooks/responsive';
import CustomStatus from '@screens/custom_status';
import EditProfile from '@screens/edit_profile';

type SelectedView = {
    id: string;
    Component: any;
}

const TabletView: Record<string, React.ComponentType<any>> = {
    [Screens.CUSTOM_STATUS]: CustomStatus,
    [Screens.EDIT_PROFILE]: EditProfile,
};

const AccountTabletView = () => {
    const [selected, setSelected] = useState<SelectedView | undefined>();
    const {isTablet} = useResponsiveInfo();

    useEffect(() => {
        const listener = DeviceEventEmitter.addListener(Events.ACCOUNT_SELECT_TABLET_VIEW, (id: string) => {
            const component = TabletView[id];
            let tabletView: SelectedView | undefined;
            if (component) {
                tabletView = {
                    Component: component,
                    id,
                };
            }
            setSelected(tabletView);
        });

        return () => listener.remove();
    }, []);

    if (!selected) {
        return null;
    }

    // Use responsive layout for better tablet experience
    if (isTablet) {
        return (
            <TabletLayout testID="account.tablet.view">
                {React.createElement(selected.Component, {
                    componentId: selected.id,
                    isTablet: true
                })}
            </TabletLayout>
        );
    }

    return React.createElement(selected.Component, {componentId: selected.id, isTablet: false});
};

export default AccountTabletView;
