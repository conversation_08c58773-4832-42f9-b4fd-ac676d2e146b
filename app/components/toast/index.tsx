// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useMemo} from 'react';
import {Dimensions, type StyleProp, Text, type TextStyle, useWindowDimensions, View, type ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {useIsTablet} from '@hooks/device';
import {useResponsiveToastDimensions} from '@hooks/responsive';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';
import {typography} from '@utils/typography';
import { button } from '@jitsi/react-native-sdk/react/features/participants-pane/components/native/styles';

type ToastProps = {
    animatedStyle: StyleProp<ViewStyle>;
    children?: React.ReactNode;
    iconName?: string;
    message?: string;
    style?: StyleProp<ViewStyle>;
    textStyle?: StyleProp<TextStyle>;
}

// Legacy export for backward compatibility
export const TOAST_HEIGHT = 56;

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme, toastHeight: number) => ({
    center: {
        alignItems: 'center',
        width: '100%',
        opacity: 0,
        justifyContent:'flex-start',
    },
    container: {
        alignItems: 'center',
        backgroundColor: theme.onlineIndicator,
        borderRadius: 8,
        elevation: 6,
        flex: 1,
        flexDirection: 'row',
        height: toastHeight,
        paddingHorizontal: 16,
        shadowColor: changeOpacity('#000', 0.12),
        shadowOffset: {width: 0, height: 4},
        shadowRadius: 6,
    },
    flex: {flex: 1},
    text: {
        color: theme.buttonColor,
        marginLeft: 10,
        ...typography('Heading', 100, 'SemiBold'),
    },
}));

const Toast = ({animatedStyle, children, style, iconName, message, textStyle}: ToastProps) => {
    const theme = useTheme();
    const toastDimensions = useResponsiveToastDimensions();
    const styles = getStyleSheet(theme, toastDimensions.height);
    const dim = useWindowDimensions();

    const containerStyle = useMemo(() => {
        const width = Math.min(dim.height, dim.width, toastDimensions.width) - toastDimensions.margin;
        return [styles.container, {width}, style];
    }, [dim, styles.container, style, toastDimensions]);

    const windowHeight = Dimensions.get('window').height;
    return (
        <Animated.View style={[
            styles.center, 
            animatedStyle,
        ]}>
            <Animated.View
             style={containerStyle}
             >
                {Boolean(iconName) &&
                <CompassIcon
                    color={theme.buttonColor}
                    name={iconName!}
                    size={18}
                    style={textStyle}
                />
                
                }
                {Boolean(message) &&
                <View style={styles.flex}>
                    <Text
                        style={[styles.text, textStyle]}
                        testID='toast.message'
                    >
                        {message}
                    </Text>
                </View>
                }
                {children}
            </Animated.View>
        </Animated.View>
    );
};

export default Toast;

