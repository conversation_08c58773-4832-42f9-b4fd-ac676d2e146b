// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {type ReactNode} from 'react';
import {Platform, Text, View, type ViewStyle} from 'react-native';

import TouchableWithFeedback from '@components/touchable_with_feedback';
import {useTheme} from '@context/theme';
import {useResponsiveDimensions, useResponsiveInfo} from '@hooks/responsive';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';
import {typography} from '@utils/typography';

type ResponsiveHeaderProps = {
    title?: string;
    subtitle?: string;
    leftComponent?: ReactNode;
    rightComponent?: ReactNode;
    centerComponent?: ReactNode;
    onTitlePress?: () => void;
    style?: ViewStyle;
    testID?: string;
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme, dimensions: any) => ({
    container: {
        height: dimensions.navigationHeight,
        backgroundColor: theme.centerChannelBg,
        borderBottomWidth: 1,
        borderBottomColor: changeOpacity(theme.centerChannelColor, 0.08),
        paddingHorizontal: dimensions.padding.container,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    leftSection: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
    },
    centerSection: {
        flex: 2,
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
    },
    centerSectionTablet: {
        flex: 3, // More space on tablets
    },
    rightSection: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    titleContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    title: {
        color: theme.centerChannelColor,
        ...typography('Heading', 200, 'SemiBold'),
        textAlign: 'center',
    },
    titleTablet: {
        ...typography('Heading', 300, 'SemiBold'), // Larger on tablets
    },
    subtitle: {
        color: changeOpacity(theme.centerChannelColor, 0.64),
        ...typography('Body', 75),
        textAlign: 'center',
        marginTop: 2,
    },
    subtitleTablet: {
        ...typography('Body', 100), // Larger on tablets
    },
    touchableTitle: {
        paddingVertical: dimensions.spacing.xs,
        paddingHorizontal: dimensions.spacing.sm,
        borderRadius: dimensions.borderRadius.small,
    },
}));

/**
 * Responsive header component that adapts to different screen sizes
 */
const ResponsiveHeader = ({
    title,
    subtitle,
    leftComponent,
    rightComponent,
    centerComponent,
    onTitlePress,
    style,
    testID,
}: ResponsiveHeaderProps) => {
    const theme = useTheme();
    const {isTablet} = useResponsiveInfo();
    const dimensions = useResponsiveDimensions();
    const styles = getStyleSheet(theme, dimensions);

    const renderTitle = () => {
        if (centerComponent) {
            return centerComponent;
        }

        if (!title) {
            return null;
        }

        const titleStyle = [
            styles.title,
            isTablet && styles.titleTablet,
        ];

        const subtitleStyle = [
            styles.subtitle,
            isTablet && styles.subtitleTablet,
        ];

        const titleContent = (
            <View style={styles.titleContainer}>
                <Text 
                    style={titleStyle}
                    numberOfLines={1}
                    testID={`${testID}.title`}
                >
                    {title}
                </Text>
                {subtitle && (
                    <Text 
                        style={subtitleStyle}
                        numberOfLines={1}
                        testID={`${testID}.subtitle`}
                    >
                        {subtitle}
                    </Text>
                )}
            </View>
        );

        if (onTitlePress) {
            return (
                <TouchableWithFeedback
                    onPress={onTitlePress}
                    style={styles.touchableTitle}
                    type={Platform.select({android: 'native', ios: 'opacity'})}
                    underlayColor={changeOpacity(theme.centerChannelColor, 0.1)}
                    testID={`${testID}.title.button`}
                >
                    {titleContent}
                </TouchableWithFeedback>
            );
        }

        return titleContent;
    };

    return (
        <View 
            style={[styles.container, style]}
            testID={testID}
        >
            <View style={styles.leftSection}>
                {leftComponent}
            </View>
            
            <View style={[
                styles.centerSection,
                isTablet && styles.centerSectionTablet,
            ]}>
                {renderTitle()}
            </View>
            
            <View style={styles.rightSection}>
                {rightComponent}
            </View>
        </View>
    );
};

export default ResponsiveHeader;
