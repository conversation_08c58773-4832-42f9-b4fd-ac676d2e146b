// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {type ReactNode, useMemo} from 'react';
import {View, type ViewStyle} from 'react-native';

import {useTheme} from '@context/theme';
import {useResponsiveDimensions, useResponsiveInfo} from '@hooks/responsive';
import {makeStyleSheetFromTheme} from '@utils/theme';

type ResponsiveGridProps = {
    children: ReactNode[];
    columns?: number;
    spacing?: number;
    style?: ViewStyle;
    itemStyle?: ViewStyle;
    testID?: string;
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme, spacing: number) => ({
    container: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        margin: -spacing / 2,
    },
    item: {
        padding: spacing / 2,
    },
}));

/**
 * Responsive grid component that adapts column count based on device type
 */
const ResponsiveGrid = ({
    children,
    columns,
    spacing,
    style,
    itemStyle,
    testID,
}: ResponsiveGridProps) => {
    const theme = useTheme();
    const {isTablet} = useResponsiveInfo();
    const dimensions = useResponsiveDimensions();
    
    // Use provided spacing or responsive default
    const gridSpacing = spacing ?? dimensions.spacing.md;
    const styles = getStyleSheet(theme, gridSpacing);
    
    // Determine column count based on device type and provided columns
    const columnCount = useMemo(() => {
        if (columns) {
            return columns;
        }
        
        // Default responsive columns
        return dimensions.gridColumns;
    }, [columns, dimensions.gridColumns]);
    
    // Calculate item width based on column count
    const itemWidth = useMemo(() => {
        return `${100 / columnCount}%`;
    }, [columnCount]);
    
    // Group children into rows for better performance
    const rows = useMemo(() => {
        const result: ReactNode[][] = [];
        for (let i = 0; i < children.length; i += columnCount) {
            result.push(children.slice(i, i + columnCount));
        }
        return result;
    }, [children, columnCount]);

    return (
        <View 
            style={[styles.container, style]}
            testID={testID}
        >
            {children.map((child, index) => (
                <View
                    key={index}
                    style={[
                        styles.item,
                        {width: itemWidth},
                        itemStyle,
                    ]}
                >
                    {child}
                </View>
            ))}
        </View>
    );
};

export default ResponsiveGrid;
