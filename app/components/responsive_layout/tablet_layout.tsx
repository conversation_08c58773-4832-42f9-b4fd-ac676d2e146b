// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {type ReactNode} from 'react';
import {View, type ViewStyle} from 'react-native';

import {useTheme} from '@context/theme';
import {useResponsiveDimensions, useResponsiveInfo} from '@hooks/responsive';
import {makeStyleSheetFromTheme} from '@utils/theme';

type TabletLayoutProps = {
    children: ReactNode;
    sidebar?: ReactNode;
    header?: ReactNode;
    footer?: ReactNode;
    sidebarWidth?: number;
    style?: ViewStyle;
    testID?: string;
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme, dimensions: any) => ({
    container: {
        flex: 1,
        backgroundColor: theme.centerChannelBg,
    },
    tabletContainer: {
        flexDirection: 'row',
        flex: 1,
    },
    phoneContainer: {
        flex: 1,
    },
    sidebar: {
        width: dimensions.sidebarWidth,
        backgroundColor: theme.sidebarBg,
        borderRightWidth: 1,
        borderRightColor: theme.centerChannelColor,
        opacity: 0.1,
    },
    mainContent: {
        flex: 1,
        backgroundColor: theme.centerChannelBg,
    },
    header: {
        backgroundColor: theme.centerChannelBg,
        borderBottomWidth: 1,
        borderBottomColor: theme.centerChannelColor,
        opacity: 0.1,
        height: dimensions.navigationHeight,
    },
    content: {
        flex: 1,
    },
    footer: {
        backgroundColor: theme.centerChannelBg,
        borderTopWidth: 1,
        borderTopColor: theme.centerChannelColor,
        opacity: 0.1,
    },
}));

/**
 * Responsive layout component that provides different layouts for tablets and phones
 */
const TabletLayout = ({
    children,
    sidebar,
    header,
    footer,
    sidebarWidth,
    style,
    testID,
}: TabletLayoutProps) => {
    const theme = useTheme();
    const {isTablet} = useResponsiveInfo();
    const dimensions = useResponsiveDimensions();
    
    // Use custom sidebar width or responsive default
    const finalSidebarWidth = sidebarWidth || dimensions.sidebarWidth;
    const stylesWithDimensions = {
        ...dimensions,
        sidebarWidth: finalSidebarWidth,
    };
    
    const styles = getStyleSheet(theme, stylesWithDimensions);

    if (!isTablet) {
        // Phone layout - stack vertically
        return (
            <View 
                style={[styles.container, styles.phoneContainer, style]}
                testID={testID}
            >
                {header && (
                    <View style={styles.header}>
                        {header}
                    </View>
                )}
                <View style={styles.content}>
                    {children}
                </View>
                {footer && (
                    <View style={styles.footer}>
                        {footer}
                    </View>
                )}
            </View>
        );
    }

    // Tablet layout - sidebar + main content
    return (
        <View 
            style={[styles.container, style]}
            testID={testID}
        >
            {header && (
                <View style={styles.header}>
                    {header}
                </View>
            )}
            <View style={styles.tabletContainer}>
                {sidebar && (
                    <View style={styles.sidebar}>
                        {sidebar}
                    </View>
                )}
                <View style={styles.mainContent}>
                    <View style={styles.content}>
                        {children}
                    </View>
                    {footer && (
                        <View style={styles.footer}>
                            {footer}
                        </View>
                    )}
                </View>
            </View>
        </View>
    );
};

export default TabletLayout;
