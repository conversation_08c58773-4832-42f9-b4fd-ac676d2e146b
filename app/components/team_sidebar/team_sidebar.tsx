// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useEffect } from 'react';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

import { TEAM_SIDEBAR_WIDTH } from '@constants/view';
import { useTheme } from '@context/theme';
import { useResponsiveDimensions } from '@hooks/responsive';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';

import AddTeam from './add_team';
import TeamList from './team_list';

type Props = {
    iconPad?: boolean;
    canJoinOtherTeams: boolean;
    hasMoreThanOneTeam: boolean;
    isFromHome?: boolean | null
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        container: {
            // Width is now handled by animated value and responsive dimensions
            height: '100%',
            backgroundColor: theme.sidebarBg,
            paddingTop: 10,
        },
        listContainer: {
            //backgroundColor: changeOpacity(theme.buttonBg,0.15),
           //  borderTopRightRadius: 12,
            //flex: 1,
        },
        listContainerFromHome: {
            //         backgroundColor: changeOpacity(theme.buttonBg,0.15),
            borderTopRightRadius: 12,
        },
        iconMargin: {
            marginTop: 44,
        },
    };
});

export default function TeamSidebar({ iconPad, canJoinOtherTeams, hasMoreThanOneTeam, isFromHome = false }: Props) {
    const responsiveDimensions = useResponsiveDimensions();
    const responsiveTeamSidebarWidth = responsiveDimensions.sidebarWidth * 0.25; // Team sidebar is smaller than main sidebar

    const initialWidth = hasMoreThanOneTeam ? responsiveTeamSidebarWidth : 0;
    const width = useSharedValue(initialWidth);
    const marginTop = useSharedValue(iconPad ? responsiveDimensions.spacing.lg : 0);
    const theme = useTheme();
    const styles = getStyleSheet(theme);

    const transform = useAnimatedStyle(() => {
        return {
            width: withTiming(width.value, { duration: 350 }),
        };
    }, []);

    const serverStyle = useAnimatedStyle(() => ({
        marginTop: withTiming(marginTop.value, { duration: 350 }),
    }), []);

    useEffect(() => {
        marginTop.value = iconPad ? responsiveDimensions.spacing.lg : 0;
    }, [iconPad, responsiveDimensions.spacing.lg]);

    useEffect(() => {
        width.value = hasMoreThanOneTeam ? responsiveTeamSidebarWidth : 0;
    }, [hasMoreThanOneTeam, responsiveTeamSidebarWidth]);

    return (
        <Animated.View style={[isFromHome && styles.listContainerFromHome //:
        // styles.container
         , 
        // transform
         ]}>
             <Animated.View style={[isFromHome &&styles.listContainerFromHome
            ,canJoinOtherTeams&&isFromHome&&
            {
                display:'flex'
                ,flexDirection:'row',
                justifyContent:'center'
                ,alignContent:'center',
             }
               //  : styles.listContainer
                 , serverStyle]}>
                <TeamList
                    isFromHome={isFromHome}
                    testID='team_sidebar.team_list' />
                {
                    (canJoinOtherTeams 
                       && !isFromHome
                )&&
                    (
                        <AddTeam />
                    )}
            </Animated.View>
        </Animated.View>
    );
}
