# Responsive Design System

This document outlines the responsive design system implemented in the React Native app to ensure optimal user experience across different device sizes and orientations.

## Overview

The responsive design system provides:
- Device type detection and classification
- Responsive breakpoints and utilities
- Scalable dimensions and spacing
- Adaptive layouts for tablets and phones
- Responsive image handling
- Typography scaling

## Device Types

The system classifies devices into six categories:

| Device Type | Screen Width | Description |
|-------------|--------------|-------------|
| `PHONE_SMALL` | ≤ 320px | Small phones (iPhone SE, small Android) |
| `PHONE_MEDIUM` | ≤ 375px | Standard phones (iPhone 12, most Android) |
| `PHONE_LARGE` | ≤ 414px | Large phones (iPhone 12 Pro Max, large Android) |
| `TABLET_SMALL` | ≤ 768px | Small tablets (iPad mini, small Android tablets) |
| `TABLET_LARGE` | ≤ 1024px | Large tablets (iPad Pro, large Android tablets) |
| `DESKTOP` | > 1024px | Desktop/very large screens |

## Core Utilities

### Device Detection

```typescript
import { getDeviceType, isTabletDevice, isPhoneDevice } from '@utils/responsive';

const deviceType = getDeviceType(); // Returns device type
const isTablet = isTabletDevice(); // Boolean
const isPhone = isPhoneDevice(); // Boolean
```

### Responsive Values

```typescript
import { getResponsiveValue } from '@utils/responsive';

const fontSize = getResponsiveValue({
  [DEVICE_TYPES.PHONE_SMALL]: 14,
  [DEVICE_TYPES.PHONE_MEDIUM]: 16,
  [DEVICE_TYPES.TABLET_SMALL]: 18,
  [DEVICE_TYPES.TABLET_LARGE]: 20,
}, 16); // fallback value
```

### Responsive Spacing

```typescript
import { getResponsiveSpacing } from '@utils/responsive';

const padding = getResponsiveSpacing(2); // 2x base spacing unit
const margin = getResponsiveSpacing(1.5); // 1.5x base spacing unit
```

## Hooks

### useResponsiveInfo

Provides comprehensive device and screen information:

```typescript
import { useResponsiveInfo } from '@hooks/responsive';

const {
  width,
  height,
  deviceType,
  isTablet,
  isPhone,
  isLandscape,
  isPortrait,
} = useResponsiveInfo();
```

### useResponsiveDimensions

Provides pre-calculated responsive dimensions:

```typescript
import { useResponsiveDimensions } from '@hooks/responsive';

const {
  sidebarWidth,
  navigationHeight,
  touchTargetMin,
  containerMaxWidth,
  gridColumns,
  spacing: { xs, sm, md, lg, xl, xxl },
  padding: { screen, container, component, small },
  margin: { section, component, small },
  borderRadius: { small, medium, large },
} = useResponsiveDimensions();
```

### useResponsiveValue

Hook version of responsive value utility:

```typescript
import { useResponsiveValue } from '@hooks/responsive';

const fontSize = useResponsiveValue({
  [DEVICE_TYPES.PHONE_SMALL]: 14,
  [DEVICE_TYPES.TABLET_SMALL]: 18,
}, 16);
```

## Layout Components

### TabletLayout

Provides different layouts for tablets and phones:

```typescript
import { TabletLayout } from '@components/responsive_layout';

<TabletLayout
  sidebar={<Sidebar />}
  header={<Header />}
  footer={<Footer />}
>
  <MainContent />
</TabletLayout>
```

### ResponsiveGrid

Adaptive grid system:

```typescript
import { ResponsiveGrid } from '@components/responsive_layout';

<ResponsiveGrid columns={2} spacing={16}>
  {items.map(item => <GridItem key={item.id} />)}
</ResponsiveGrid>
```

### ResponsiveHeader

Adaptive header component:

```typescript
import { ResponsiveHeader } from '@components/responsive_layout';

<ResponsiveHeader
  title="Screen Title"
  subtitle="Optional subtitle"
  leftComponent={<BackButton />}
  rightComponent={<MenuButton />}
/>
```

## Image Responsiveness

### Responsive Image Calculations

```typescript
import { calculateResponsiveDimensions } from '@utils/images';

const { width, height } = calculateResponsiveDimensions(
  originalHeight,
  originalWidth,
  viewportWidth,
  viewportHeight,
  deviceWidth
);
```

### useResponsiveImageDimensions Hook

```typescript
import { useResponsiveImageDimensions } from '@hooks/responsive';

const { width, height } = useResponsiveImageDimensions(
  originalWidth,
  originalHeight,
  maxWidthPercentage
);
```

## Best Practices

### 1. Use Responsive Utilities

❌ **Don't use hardcoded dimensions:**
```typescript
const styles = StyleSheet.create({
  container: {
    width: 300,
    height: 50,
    padding: 16,
  },
});
```

✅ **Use responsive utilities:**
```typescript
const styles = useResponsiveStyles((dimensions) => ({
  container: {
    width: dimensions.containerMaxWidth,
    height: dimensions.touchTargetMin,
    padding: dimensions.padding.container,
  },
}));
```

### 2. Leverage Device-Specific Layouts

❌ **Don't use the same layout for all devices:**
```typescript
<View style={styles.container}>
  <Sidebar />
  <MainContent />
</View>
```

✅ **Use responsive layouts:**
```typescript
<TabletLayout sidebar={<Sidebar />}>
  <MainContent />
</TabletLayout>
```

### 3. Scale Typography Appropriately

❌ **Don't use fixed font sizes:**
```typescript
const styles = {
  title: { fontSize: 18 },
  body: { fontSize: 14 },
};
```

✅ **Use responsive font sizes:**
```typescript
const titleSize = useResponsiveFontSize(18);
const bodySize = useResponsiveFontSize(14);
```

## Migration Guide

### Updating Existing Components

1. **Replace hardcoded dimensions:**
   - Find hardcoded widths, heights, padding, margins
   - Replace with responsive utilities or hooks

2. **Update device detection:**
   - Replace `useIsTablet()` with `useResponsiveInfo().isTablet`
   - Use device type for more granular control

3. **Implement responsive layouts:**
   - Use `TabletLayout` for major screens
   - Use `ResponsiveGrid` for list layouts
   - Use `ResponsiveHeader` for navigation

4. **Update image handling:**
   - Use `calculateResponsiveDimensions` for image sizing
   - Use `useResponsiveImageDimensions` hook in components

## Testing

Run the responsive design tests:

```bash
npm test -- responsive.test.ts
```

Test on different device sizes:
- iPhone SE (375x667)
- iPhone 12 (390x844)
- iPad (768x1024)
- iPad Pro (1024x1366)

## Performance Considerations

- Responsive calculations are memoized in hooks
- Device type detection is cached
- Use `useResponsiveStyles` for dynamic style calculations
- Avoid inline responsive calculations in render methods
